# 测试指南

本文档提供了 ShipAny 项目的完整测试策略和测试流程，包括本地测试、构建测试、内容验证和自动化测试。

## 测试策略概览

### 测试层级

1. **单元测试**：组件和工具函数测试
2. **集成测试**：页面和 API 路由测试
3. **内容测试**：MDX 内容和 SEO 文件验证
4. **端到端测试**：完整用户流程测试
5. **性能测试**：构建大小和加载速度测试

### 测试环境

- **开发环境**：`pnpm dev` - 实时测试和调试
- **构建环境**：`pnpm build` - 生产构建验证
- **生产环境**：`pnpm start` - 生产服务器测试

## 本地开发测试

### 启动测试环境

```bash
# 1. 启动开发服务器
pnpm dev

# 2. 验证服务器启动
# 访问 http://localhost:3000
# 检查控制台无错误信息
```

### 功能测试清单

#### 基础功能测试

- [ ] 首页正常加载
- [ ] 导航菜单功能正常
- [ ] 语言切换功能正常
- [ ] 响应式设计在不同设备上正常

#### 内容页面测试

```bash
# 博客页面测试
访问 /blogs                    # 博客列表页
访问 /blogs/[slug]             # 博客详情页
访问 /zh/blogs                 # 中文博客列表
访问 /zh/blogs/[slug]          # 中文博客详情

# 产品页面测试
访问 /products                 # 产品列表页
访问 /products/[slug]          # 产品详情页
访问 /zh/products              # 中文产品列表
访问 /zh/products/[slug]       # 中文产品详情

# 案例研究测试
访问 /case-studies             # 案例列表页
访问 /case-studies/[slug]      # 案例详情页
访问 /zh/case-studies          # 中文案例列表
访问 /zh/case-studies/[slug]   # 中文案例详情
```

#### 数据库功能测试

```bash
# Posts 功能测试（现有功能）
访问 /posts                    # 文章列表页
访问 /posts/[slug]             # 文章详情页
访问 /zh/posts                 # 中文文章列表
访问 /zh/posts/[slug]          # 中文文章详情
```

### 内容验证测试

#### MDX 内容测试

```bash
# 1. 创建测试内容
echo '---
title: "测试文章"
slug: "test-post"
description: "这是一个测试文章"
publishedAt: "2024-01-01"
---

# 测试内容

这是测试内容。
' > content/blogs/zh/test-post.mdx

# 2. 验证内容处理
# 检查开发服务器控制台
# 访问 /zh/blogs/test-post

# 3. 清理测试内容
rm content/blogs/zh/test-post.mdx
```

#### SEO 文件测试

```bash
# 1. 生成 SEO 文件
pnpm generate:content

# 2. 验证生成的文件
ls -la public/sitemap.xml      # 检查 sitemap 存在
ls -la public/rss.xml          # 检查英文 RSS 存在
ls -la public/rss-zh.xml       # 检查中文 RSS 存在

# 3. 验证文件内容
curl http://localhost:3000/sitemap.xml    # 检查 sitemap 可访问
curl http://localhost:3000/rss.xml        # 检查 RSS 可访问
curl http://localhost:3000/rss-zh.xml     # 检查中文 RSS 可访问
```

## 构建测试

### 生产构建测试

```bash
# 1. 执行完整构建
pnpm build

# 2. 检查构建输出
ls -la .next/static/           # 静态资源
ls -la .next/server/           # 服务器代码
ls -la public/                 # 公共文件

# 3. 启动生产服务器
pnpm start

# 4. 验证生产环境
访问 http://localhost:3000
检查页面加载速度
检查控制台无错误
```

### 构建产物验证

```bash
# 检查关键文件存在
test -f .next/BUILD_ID && echo "Build ID exists"
test -f public/sitemap.xml && echo "Sitemap exists"
test -f public/rss.xml && echo "RSS exists"
test -f .contentlayer/generated/index.mjs && echo "Content index exists"

# 检查构建大小
pnpm analyze                   # 分析构建包大小
```

## 内容质量测试

### 内容验证清单

#### MDX 文件验证

- [ ] Frontmatter 格式正确（YAML 语法）
- [ ] 必需字段完整（title, slug）
- [ ] 可选字段格式正确（publishedAt 日期格式）
- [ ] 图片路径有效
- [ ] 内部链接正确
- [ ] 标签合理且一致

#### 多语言内容验证

- [ ] 英文和中文版本内容对应
- [ ] Slug 在不同语言版本中保持一致
- [ ] 图片和资源在不同语言版本中可用
- [ ] 元数据翻译准确

#### SEO 优化验证

- [ ] 页面标题长度适中（50-60字符）
- [ ] 描述长度适中（150-160字符）
- [ ] 图片包含 alt 属性
- [ ] 标题层级合理（H1-H6）
- [ ] 内部链接建设合理

### 自动化内容验证

```bash
# 创建内容验证脚本
cat > scripts/validate-content.js << 'EOF'
import { allBlogs, allProducts, allCaseStudies } from '../.contentlayer/generated/index.mjs'

function validateContent() {
  const allContent = [...allBlogs, ...allProducts, ...allCaseStudies]
  
  allContent.forEach(item => {
    // 验证必需字段
    if (!item.title) console.error(`Missing title: ${item._raw.sourceFilePath}`)
    if (!item.slug) console.error(`Missing slug: ${item._raw.sourceFilePath}`)
    
    // 验证描述长度
    if (item.description && item.description.length > 160) {
      console.warn(`Description too long: ${item._raw.sourceFilePath}`)
    }
    
    // 验证发布日期格式
    if (item.publishedAt && !/^\d{4}-\d{2}-\d{2}$/.test(item.publishedAt)) {
      console.error(`Invalid date format: ${item._raw.sourceFilePath}`)
    }
  })
  
  console.log(`Validated ${allContent.length} content items`)
}

validateContent()
EOF

# 运行验证
node scripts/validate-content.js
```

## 性能测试

### 构建性能测试

```bash
# 1. 测试构建时间
time pnpm build

# 2. 分析构建大小
pnpm analyze

# 3. 检查关键指标
# - 首次内容绘制 (FCP)
# - 最大内容绘制 (LCP)
# - 累积布局偏移 (CLS)
# - 首次输入延迟 (FID)
```

### 页面加载测试

```bash
# 使用 Lighthouse 测试
npx lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html

# 检查关键页面
npx lighthouse http://localhost:3000/blogs --output=json
npx lighthouse http://localhost:3000/products --output=json
npx lighthouse http://localhost:3000/case-studies --output=json
```

## 自动化测试

### 单元测试设置

```bash
# 安装测试依赖
pnpm add -D jest @testing-library/react @testing-library/jest-dom

# 创建 Jest 配置
cat > jest.config.js << 'EOF'
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
}

module.exports = createJestConfig(customJestConfig)
EOF

# 创建测试设置文件
echo "import '@testing-library/jest-dom'" > jest.setup.js
```

### 示例测试用例

```javascript
// __tests__/components/mdx.test.js
import { render, screen } from '@testing-library/react'
import { Mdx } from '@/components/mdx'

describe('MDX Component', () => {
  it('renders MDX content correctly', () => {
    const mockCode = 'function _createMdxContent(props) { return React.createElement("h1", null, "Test"); }'
    
    render(<Mdx code={mockCode} />)
    
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()
  })
})

// __tests__/pages/blogs.test.js
import { render, screen } from '@testing-library/react'
import BlogsPage from '@/app/[locale]/blogs/page'

jest.mock('../.contentlayer/generated/index.mjs', () => ({
  allBlogs: [
    {
      title: 'Test Blog',
      slug: 'test-blog',
      description: 'Test description',
      lang: 'en'
    }
  ]
}))

describe('Blogs Page', () => {
  it('renders blog list correctly', () => {
    render(<BlogsPage params={{ locale: 'en' }} />)
    
    expect(screen.getByText('Test Blog')).toBeInTheDocument()
  })
})
```

### 运行测试

```bash
# 运行所有测试
pnpm test

# 运行特定测试
pnpm test -- __tests__/components/

# 运行测试并生成覆盖率报告
pnpm test -- --coverage

# 监听模式运行测试
pnpm test -- --watch
```

## 端到端测试

### Playwright 设置

```bash
# 安装 Playwright
pnpm add -D @playwright/test

# 初始化 Playwright
npx playwright install

# 创建 E2E 测试配置
cat > playwright.config.ts << 'EOF'
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
  webServer: {
    command: 'pnpm dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
})
EOF
```

### 示例 E2E 测试

```javascript
// e2e/navigation.spec.ts
import { test, expect } from '@playwright/test'

test('navigation works correctly', async ({ page }) => {
  await page.goto('/')
  
  // 测试导航到博客页面
  await page.click('text=Blogs')
  await expect(page).toHaveURL('/blogs')
  
  // 测试语言切换
  await page.click('[data-testid="language-switcher"]')
  await page.click('text=中文')
  await expect(page).toHaveURL('/zh/blogs')
})

test('blog content loads correctly', async ({ page }) => {
  await page.goto('/blogs')
  
  // 点击第一篇博客
  await page.click('.blog-item:first-child a')
  
  // 验证博客内容页面
  await expect(page.locator('h1')).toBeVisible()
  await expect(page.locator('.blog-content')).toBeVisible()
})
```

### 运行 E2E 测试

```bash
# 运行所有 E2E 测试
npx playwright test

# 运行特定测试
npx playwright test navigation

# 调试模式运行
npx playwright test --debug

# 生成测试报告
npx playwright show-report
```

## 测试最佳实践

### 测试策略

1. **测试金字塔**：更多单元测试，适量集成测试，少量 E2E 测试
2. **快速反馈**：优先运行快速测试，慢速测试放在 CI 中
3. **真实场景**：测试用例应该反映真实用户行为
4. **数据隔离**：每个测试应该独立，不依赖其他测试

### 持续集成

```yaml
# .github/workflows/test.yml
name: Test
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - run: pnpm install
      - run: pnpm lint
      - run: pnpm test
      - run: pnpm build
      - run: npx playwright install
      - run: npx playwright test
```

### 测试维护

1. **定期更新**：保持测试用例与功能同步
2. **清理无效测试**：删除过时或无效的测试
3. **性能监控**：监控测试执行时间，优化慢速测试
4. **文档更新**：保持测试文档与实际测试同步

## 故障排除

### 常见测试问题

1. **测试环境问题**

   ```bash
   # 清理测试环境
   rm -rf .next node_modules/.cache
   pnpm install
   ```

2. **内容测试失败**

   ```bash
   # 重新生成内容
   rm -rf .contentlayer
   pnpm contentlayer build
   ```

3. **E2E 测试超时**

   ```bash
   # 增加超时时间
   npx playwright test --timeout=60000
   ```

### 调试技巧

1. **使用调试模式**：`pnpm test -- --debug`
2. **查看测试覆盖率**：`pnpm test -- --coverage`
3. **单独运行失败测试**：`pnpm test -- --testNamePattern="specific test"`
4. **使用浏览器调试**：`npx playwright test --headed`

通过遵循这个测试指南，可以确保 ShipAny 项目的质量和稳定性，及早发现和修复问题。
